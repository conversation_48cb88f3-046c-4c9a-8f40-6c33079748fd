"""
# analyze子包

子包路径：`sweeper400.analyze`

包含**信号和数据处理**相关的模块和类。
"""

# 将模块功能提升至包级别，可缩短外部import语句
from .my_dtypes import PositiveInt, PositiveFloat, SamplingInfo, Waveform
from .basic_sine import (
    init_sampling_info,
    sine_wave_vvi,
    extract_single_tone_information_vvi,
)
from .waveform_generator import WaveformGenerator, SineGenerator

# 控制 import * 的行为
__all__ = [
    "PositiveInt",
    "PositiveFloat",
    "SamplingInfo",
    "Waveform",
    "init_sampling_info",
    "sine_wave_vvi",
    "extract_single_tone_information_vvi",
    "WaveformGenerator",
    "SineGenerator",
]
