---
type: "always_apply"
---

<Measuring_Rules>
以下Rules适用于NI数据采集相关功能（"pysweep\measure"目录下）的开发。
- 我们正在python中使用nidaqmx包，控制NI数据采集卡进行数据采集工作。
- 如需查看nidaqmx包具体细节/官方例程，我已经将该包的完整代码仓库clone在本工作区的"参考资料\nidaqmx-python-master"处。（但你无需使用该目录中的文件，因为nidaqmx包已经安装在我们的Python环境中，你可以直接import。）
- 目前有一台型号为PXIe-1090的NI机箱（名称为"PXIChassis1"）通过雷电线连接至本PC的雷电接口（NI MAX软件显示连接正常，并可正常开始数据采集任务）。其上搭载两张PXIe-4468 DSA Analog I/O板卡，名称分别为"402Dev2Slot2"和"402Dev2Slot3"。两张板卡均各有"ai0"和"ai1"两个输入通道，以及"ao0""ao1"两个输出通道（因此整台机箱共有4个输入通道和4个输出通道）。板卡官方手册显示，其可以使用机箱中的PXIe_CLK100时钟作为硬件采样时钟源。
- 所有ai和ao通道的电压范围均为±10.0 V (true peak)，或RMS 7.07 V (Sine Wave)。为了设备的安全，任何情况下都不要令电压范围超过该值。（你可以默认使用该值作为AI/AO任务的相应参数）
- 我们目前的具体工作方式是，在"pysweep\measure\draft1.py"这一模块中实现所有数据采集相关的函数/类/方法/属性，并在"tests"文件夹中调用"draft1.py"模块，完成相关操作。
</Measuring_Rules>